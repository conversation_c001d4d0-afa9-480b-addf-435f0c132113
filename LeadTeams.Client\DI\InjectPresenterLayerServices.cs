﻿namespace LeadTeams.Client.DI
{
    public static class InjectPresenterLayerServices
    {
        public static IServiceCollection InjectPresenterServices(this IServiceCollection services)
        {
            services.AddDesktopCompositeTimezoneHandling<InMemoryDesktopUserContext, ConfigurationDesktopSettingsService>();
            services.InjectSignalRChatIntegrationServices();
            services.AddSingleton<SignalRChat.Views.ChatView>();
            services.AddSingleton<MessengerView>();
            services.InjectSignalRChatServices<LeadTeamsSignalRConnectionManager>();
            services.InjectServiceServices();
            services.AddSingleton<ISession, LocalSession>();
            services.AddTransient<IAuthenticationValidationService, AuthenticationLoginValidationService>();
            services.AddTransient<LoginView>();
            services.AddTransient<AskLeaveListView>();
            services.AddTransient<AskLeaveView>();
            services.AddTransient<IdlePopupView>();
            services.AddTransient<ImInAMeetingPopupView>();
            services.AddTransient<SnoozePopupView>();
            services.AddTransient<StopPopupView>();
            services.AddTransient<InMeetingPopupView>();
            services.AddTransient<IScreenShotSaver, ScreenShotSaver>();
            services.AddTransient<IAttendanceLogger, AttendanceLogger>();
            services.AddTransient<IUnplannedShutdownChecker, UnplannedShutdownChecker>();
            services.AddTransient<IDatabaseSyncService, DatabaseSyncService>();
            services.AddTransient<IPopupService, PopupService>();
            services.AddTransient<IEmployeeTrackerView, EmployeeTrackerView>();
            services.AddTransient<IServices, EmployeeTrackerView>();
            services.AddTransient<EmployeeTrackerView>();

            // Register the authentication notification service
            services.AddSingleton<AuthenticationNotificationService>();

            return services;
        }
    }
}
