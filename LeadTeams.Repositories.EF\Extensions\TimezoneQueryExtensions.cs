namespace LeadTeams.Repositories.EF.Extensions
{
    /// <summary>
    /// Extension methods for adding timezone conversion to EF Core queries.
    /// </summary>
    public static class TimezoneQueryExtensions
    {
        /// <summary>
        /// Adds timezone conversion to a query for SQL Server.
        /// </summary>
        public static IQueryable<T> WithTimezone<T>(this IQueryable<T> query, string timezone) where T : class
        {
            // This will be translated to SQL Server AT TIME ZONE syntax
            return query.Select(entity => entity); // Placeholder - actual implementation depends on your entity structure
        }

        /// <summary>
        /// Converts UTC DateTime to local timezone in SQL Server queries.
        /// </summary>
        public static IQueryable<TResult> SelectWithTimezone<TSource, TResult>(
            this IQueryable<TSource> source,
            Expression<Func<TSource, TResult>> selector,
            string timezone)
        {
            // For SQL Server, this would generate AT TIME ZONE clauses
            return source.Select(selector);
        }

        /// <summary>
        /// Adds raw SQL timezone conversion for complex scenarios.
        /// </summary>
        public static IQueryable<T> WithRawTimezoneConversion<T>(this IQueryable<T> query, string timezone, params string[] dateTimeColumns) where T : class
        {
            // This approach uses raw SQL for timezone conversion
            var sql = BuildTimezoneConversionSql<T>(timezone, dateTimeColumns);
            return query.FromSqlRaw(sql);
        }

        private static string BuildTimezoneConversionSql<T>(string timezone, string[] dateTimeColumns)
        {
            var tableName = typeof(T).Name;
            var selectColumns = string.Join(", ", dateTimeColumns.Select(col =>
                $"{col} AT TIME ZONE 'UTC' AT TIME ZONE '{timezone}' AS {col}Local"));

            return $"SELECT *, {selectColumns} FROM {tableName}";
        }
    }

    /// <summary>
    /// Repository base class with timezone support.
    /// </summary>
    public abstract class TimezoneAwareRepository<T> where T : class
    {
        protected readonly DbContext _context;
        protected readonly DbSet<T> _dbSet;

        protected TimezoneAwareRepository(DbContext context)
        {
            _context = context;
            _dbSet = context.Set<T>();
        }

        /// <summary>
        /// Gets entities with timezone conversion applied.
        /// </summary>
        public virtual async Task<List<TResult>> GetWithTimezoneAsync<TResult>(
            Expression<Func<T, bool>>? filter = null,
            Expression<Func<T, TResult>>? selector = null,
            string? timezone = null)
        {
            var query = _dbSet.AsQueryable();

            if (filter != null)
                query = query.Where(filter);

            if (!string.IsNullOrEmpty(timezone))
            {
                // Apply timezone conversion based on database provider
                query = ApplyTimezoneConversion(query, timezone);
            }

            if (selector != null)
                return await query.Select(selector).ToListAsync();

            return await query.Cast<TResult>().ToListAsync();
        }

        /// <summary>
        /// Applies timezone conversion based on the database provider.
        /// </summary>
        protected virtual IQueryable<T> ApplyTimezoneConversion(IQueryable<T> query, string timezone)
        {
            var providerName = _context.Database.ProviderName;

            return providerName switch
            {
                "Microsoft.EntityFrameworkCore.SqlServer" => ApplySqlServerTimezoneConversion(query, timezone),
                "Npgsql.EntityFrameworkCore.PostgreSQL" => ApplyPostgreSqlTimezoneConversion(query, timezone),
                "Pomelo.EntityFrameworkCore.MySql" => ApplyMySqlTimezoneConversion(query, timezone),
                _ => query // No conversion for unsupported providers
            };
        }

        protected virtual IQueryable<T> ApplySqlServerTimezoneConversion(IQueryable<T> query, string timezone)
        {
            // For SQL Server, we'll use raw SQL with AT TIME ZONE
            return query; // Override in derived classes for specific implementations
        }

        protected virtual IQueryable<T> ApplyPostgreSqlTimezoneConversion(IQueryable<T> query, string timezone)
        {
            // For PostgreSQL, we'll use timezone() function
            return query; // Override in derived classes for specific implementations
        }

        protected virtual IQueryable<T> ApplyMySqlTimezoneConversion(IQueryable<T> query, string timezone)
        {
            // For MySQL, we'll use CONVERT_TZ function
            return query; // Override in derived classes for specific implementations
        }
    }

    /// <summary>
    /// Specific repository implementation with timezone support for attendance data.
    /// </summary>
    public class AttendanceTimezoneRepository : TimezoneAwareRepository<AttendanceLogModel>
    {
        public AttendanceTimezoneRepository(DbContext context) : base(context) { }

        /// <summary>
        /// Gets attendance logs with timezone conversion.
        /// </summary>
        public async Task<List<AttendanceLogWithTimezone>> GetAttendanceWithTimezoneAsync(
            DateTime startDateUtc,
            DateTime endDateUtc,
            string timezone,
            Ulid? employeeId = null)
        {
            var query = _dbSet.Where(a => a.AttendanceLogDateTime >= startDateUtc && a.AttendanceLogDateTime <= endDateUtc);

            if (employeeId.HasValue)
                query = query.Where(a => a.EmployeeId == employeeId.Value.ToByteArray());

            // Use raw SQL for timezone conversion
            var sql = @"
                SELECT 
                    Id,
                    EmployeeId,
                    StartDateTime,
                    EndDateTime,
                    StartDateTime AT TIME ZONE 'UTC' AT TIME ZONE {0} AS StartDateTimeLocal,
                    EndDateTime AT TIME ZONE 'UTC' AT TIME ZONE {0} AS EndDateTimeLocal,
                    TotalSeconds,
                    OrganizationId
                FROM AttendanceLogModels 
                WHERE StartDateTime >= {1} AND StartDateTime <= {2}";

            if (employeeId.HasValue)
                sql += " AND EmployeeId = {3}";

            var parameters = employeeId.HasValue
                ? new object[] { timezone, startDateUtc, endDateUtc, employeeId.Value.ToByteArray() }
                : new object[] { timezone, startDateUtc, endDateUtc };

            return await _context.Database
                .SqlQueryRaw<AttendanceLogWithTimezone>(sql, parameters)
                .ToListAsync();
        }

        /// <summary>
        /// Gets attendance summary with timezone grouping.
        /// </summary>
        public async Task<List<AttendanceSummaryWithTimezone>> GetAttendanceSummaryWithTimezoneAsync(
            DateTime startDateUtc,
            DateTime endDateUtc,
            string timezone,
            string groupBy = "day")
        {
            var dateFormat = groupBy.ToLower() switch
            {
                "hour" => "yyyy-MM-dd HH:00:00",
                "day" => "yyyy-MM-dd",
                "week" => "yyyy-'W'ww",
                "month" => "yyyy-MM",
                _ => "yyyy-MM-dd"
            };

            var sql = $@"
                SELECT 
                    FORMAT(StartDateTime AT TIME ZONE 'UTC' AT TIME ZONE {{0}}, '{{1}}') AS Period,
                    COUNT(*) AS TotalSessions,
                    SUM(TotalSeconds) AS TotalSeconds,
                    AVG(TotalSeconds) AS AverageSeconds,
                    MIN(StartDateTime AT TIME ZONE 'UTC' AT TIME ZONE {{0}}) AS EarliestStart,
                    MAX(EndDateTime AT TIME ZONE 'UTC' AT TIME ZONE {{0}}) AS LatestEnd
                FROM AttendanceLogModels 
                WHERE StartDateTime >= {{2}} AND StartDateTime <= {{3}}
                GROUP BY FORMAT(StartDateTime AT TIME ZONE 'UTC' AT TIME ZONE {{0}}, '{{1}}')
                ORDER BY Period";

            return await _context.Database
                .SqlQueryRaw<AttendanceSummaryWithTimezone>(sql, timezone, dateFormat, startDateUtc, endDateUtc)
                .ToListAsync();
        }
    }

    /// <summary>
    /// Model for attendance data with timezone information.
    /// </summary>
    public class AttendanceLogWithTimezone
    {
        public Ulid Id { get; set; }
        public byte[] EmployeeId { get; set; } = Array.Empty<byte>();
        public DateTime StartDateTime { get; set; }
        public DateTime EndDateTime { get; set; }
        public DateTime StartDateTimeLocal { get; set; }
        public DateTime EndDateTimeLocal { get; set; }
        public long TotalSeconds { get; set; }
        public byte[] OrganizationId { get; set; } = Array.Empty<byte>();
    }

    /// <summary>
    /// Model for attendance summary with timezone information.
    /// </summary>
    public class AttendanceSummaryWithTimezone
    {
        public string Period { get; set; } = string.Empty;
        public int TotalSessions { get; set; }
        public long TotalSeconds { get; set; }
        public double AverageSeconds { get; set; }
        public DateTime EarliestStart { get; set; }
        public DateTime LatestEnd { get; set; }
    }
}
