﻿namespace LeadTeams.Client.Popups
{
    public partial class SnoozePopupView : LeadTeamsForm
    {
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public Enums.SnoozePopUpResult SnoozePopUpResult;
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public DateTime SnoozeTime => dtpSnooze.ValueDateTime;

        private readonly ISession _session;

        //For design time support
        public SnoozePopupView() : base()
        {
            InitializeComponent();
        }

        public SnoozePopupView(ISession session)
        {
            _session = session;
            _session.AppLogger.LogInformation($"Initialize Component");
            InitializeComponent();
            _session.AppLogger.LogInformation($"Associate And Raise Events");
            AssociateAndRaiseViewEvents();
        }

        private void AssociateAndRaiseViewEvents()
        {
            btnSnooze.Click += OnSnoozeTimeSet;
            btnStartWork.Click += (s, e) => SetResult(Enums.SnoozePopUpResult.StartWork);
            btnShutdown.Click += (s, e) => SetResult(Enums.SnoozePopUpResult.Shutdown);
        }

        private void OnSnoozeTimeSet(object? sender, EventArgs e)
        {
            if (dtpSnooze.ValueDateTime < DateTime.Now)
            {
                MessageBox.Show("You can't select a past date and time.");
                return;
            }

            SetResult(Enums.SnoozePopUpResult.Snooze);
        }

        private void SetResult(Enums.SnoozePopUpResult result)
        {
            _session.AppLogger.LogDebug($"Setting SnoozePopUpResult to [{result}]");
            SnoozePopUpResult = result;
            DialogResult = DialogResult.OK;
        }
    }
}
