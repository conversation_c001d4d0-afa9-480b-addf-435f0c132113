﻿namespace LeadTeams.API.Controllers.Reports
{
    [Authorize]
    [Route("api/reports/[controller]")]
    [ApiController]
    [Timezone] // Enable timezone detection for all attendance endpoints
    public class AttendanceController : ControllerBase
    {
        private readonly IAttendanceService _attendanceService;

        public AttendanceController(IAttendanceService attendanceService)
        {
            _attendanceService = attendanceService;
        }

        [HttpGet("GetEmployeeAttendanceLog")]
        public IActionResult GetEmployeeAttendanceLog(string groupBy, Ulid organizationId, Ulid? employeeId = null, DateTime? startDate = null, DateTime? endDate = null)
        {
            try
            {
                var result = _attendanceService.GetEmployeeAttendanceLog(groupBy, organizationId, employeeId, startDate, endDate);
                return Ok(result);

                // Convert client-provided dates to UTC for database queries
                DateTime? startUtc = startDate.HasValue ? HttpContext.ToUtcFromClientTimezone(startDate.Value) : null;
                DateTime? endUtc = endDate.HasValue ? HttpContext.ToUtcFromClientTimezone(endDate.Value) : null;

                // Return timezone-aware response
                var response = new
                {
                    Data = result,
                    QueryParameters = new
                    {
                        GroupBy = groupBy,
                        OrganizationId = organizationId,
                        EmployeeId = employeeId,
                        StartDateUtc = startUtc,
                        EndDateUtc = endUtc,
                        ClientTimezone = HttpContext.GetClientTimezone()
                    },
                    Instructions = "All DateTime values in the response are in UTC format. Convert to your local timezone as needed."
                };

                return Ok(response.ToTimezoneAwareResponse(HttpContext));
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }
    }
}
