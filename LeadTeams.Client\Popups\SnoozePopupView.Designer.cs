﻿namespace LeadTeams.Client.Popups
{
    partial class SnoozePopupView
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            tlpMain = new LeadTeamsTableLayoutPanel();
            dtpSnooze = new LeadTeamsLabeledDateTimePicker();
            btnShutdown = new LeadTeamsButton();
            btnStartWork = new LeadTeamsButton();
            btnSnooze = new LeadTeamsButton();
            tlpMain.SuspendLayout();
            SuspendLayout();
            // 
            // tlpMain
            // 
            tlpMain.BackColor = Color.FromArgb(234, 242, 248);
            tlpMain.ColumnCount = 3;
            tlpMain.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.33333F));
            tlpMain.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.3333321F));
            tlpMain.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.3333321F));
            tlpMain.Controls.Add(dtpSnooze, 0, 0);
            tlpMain.Controls.Add(btnShutdown, 2, 1);
            tlpMain.Controls.Add(btnStartWork, 0, 1);
            tlpMain.Controls.Add(btnSnooze, 0, 1);
            tlpMain.Dock = DockStyle.Fill;
            tlpMain.Font = new Font("Novus-Regular", 11F, FontStyle.Regular, GraphicsUnit.Point, 0);
            tlpMain.ForeColor = Color.FromArgb(22, 71, 117);
            tlpMain.Location = new Point(0, 0);
            tlpMain.Margin = new Padding(4);
            tlpMain.Name = "tlpMain";
            tlpMain.RightToLeft = RightToLeft.No;
            tlpMain.RowCount = 2;
            tlpMain.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            tlpMain.RowStyles.Add(new RowStyle(SizeType.Absolute, 70F));
            tlpMain.RowStyles.Add(new RowStyle(SizeType.Absolute, 28F));
            tlpMain.Size = new Size(434, 111);
            tlpMain.TabIndex = 1;
            // 
            // dtpSnooze
            // 
            dtpSnooze.Checked = true;
            tlpMain.SetColumnSpan(dtpSnooze, 3);
            dtpSnooze.CustomFormat = "yyyy/MM/dd hh:mm:ss tt";
            dtpSnooze.Dock = DockStyle.Fill;
            dtpSnooze.Format = DateTimePickerFormat.Custom;
            dtpSnooze.LabelValue = "Set Snooze";
            dtpSnooze.Location = new Point(4, 4);
            dtpSnooze.Margin = new Padding(4);
            dtpSnooze.Name = "dtpSnooze";
            dtpSnooze.ShowCheckBox = false;
            dtpSnooze.ShowUpDown = false;
            dtpSnooze.Size = new Size(426, 33);
            dtpSnooze.TabIndex = 4;
            // 
            // btnShutdown
            // 
            btnShutdown.BackColor = Color.FromArgb(22, 71, 117);
            btnShutdown.Dock = DockStyle.Fill;
            btnShutdown.FlatStyle = FlatStyle.Flat;
            btnShutdown.Font = new Font("Novus-Regular", 11F, FontStyle.Regular, GraphicsUnit.Point, 0);
            btnShutdown.ForeColor = Color.FromArgb(234, 242, 248);
            btnShutdown.ImagesProperties.BackgroundImageSize = new Size(18, 18);
            btnShutdown.ImagesProperties.ImageFixedSize = new Size(24, 24);
            btnShutdown.ImagesProperties.ImageMaxSize = new Size(24, 24);
            btnShutdown.ImagesProperties.ImageSizeMode = Desktop.Controls.Core.Helper.Enums.SizeMode.Stretch;
            btnShutdown.ImagesProperties.OriginalImageSize = new Size(18, 18);
            btnShutdown.Location = new Point(292, 45);
            btnShutdown.Margin = new Padding(4);
            btnShutdown.MinimumSize = new Size(64, 36);
            btnShutdown.Name = "btnShutdown";
            btnShutdown.PaletteProperties.CustomBackColor = Desktop.Controls.Core.Helper.Enums.ColorsList.Color1;
            btnShutdown.PaletteProperties.CustomFont = Desktop.Controls.Core.Helper.Enums.FontsList.Font1;
            btnShutdown.PaletteProperties.CustomForeColor = Desktop.Controls.Core.Helper.Enums.ColorsList.Color8;
            btnShutdown.RadiusProperties.BorderColor = Color.Transparent;
            btnShutdown.RadiusProperties.BorderRadius = 10;
            btnShutdown.RadiusProperties.BorderSize = 0;
            btnShutdown.RightToLeft = RightToLeft.No;
            btnShutdown.Size = new Size(138, 62);
            btnShutdown.TabIndex = 3;
            btnShutdown.Text = "Shutdown";
            btnShutdown.UseVisualStyleBackColor = true;
            // 
            // btnStartWork
            // 
            btnStartWork.BackColor = Color.FromArgb(22, 71, 117);
            btnStartWork.Dock = DockStyle.Fill;
            btnStartWork.FlatStyle = FlatStyle.Flat;
            btnStartWork.Font = new Font("Novus-Regular", 11F, FontStyle.Regular, GraphicsUnit.Point, 0);
            btnStartWork.ForeColor = Color.FromArgb(234, 242, 248);
            btnStartWork.ImagesProperties.BackgroundImageSize = new Size(18, 18);
            btnStartWork.ImagesProperties.ImageFixedSize = new Size(24, 24);
            btnStartWork.ImagesProperties.ImageMaxSize = new Size(24, 24);
            btnStartWork.ImagesProperties.ImageSizeMode = Desktop.Controls.Core.Helper.Enums.SizeMode.Stretch;
            btnStartWork.ImagesProperties.OriginalImageSize = new Size(18, 18);
            btnStartWork.Location = new Point(4, 45);
            btnStartWork.Margin = new Padding(4);
            btnStartWork.MinimumSize = new Size(64, 36);
            btnStartWork.Name = "btnStartWork";
            btnStartWork.PaletteProperties.CustomBackColor = Desktop.Controls.Core.Helper.Enums.ColorsList.Color1;
            btnStartWork.PaletteProperties.CustomFont = Desktop.Controls.Core.Helper.Enums.FontsList.Font1;
            btnStartWork.PaletteProperties.CustomForeColor = Desktop.Controls.Core.Helper.Enums.ColorsList.Color8;
            btnStartWork.RadiusProperties.BorderColor = Color.Transparent;
            btnStartWork.RadiusProperties.BorderRadius = 10;
            btnStartWork.RadiusProperties.BorderSize = 0;
            btnStartWork.RightToLeft = RightToLeft.No;
            btnStartWork.Size = new Size(136, 62);
            btnStartWork.TabIndex = 2;
            btnStartWork.Text = "Start Work";
            btnStartWork.UseVisualStyleBackColor = true;
            // 
            // btnSnooze
            // 
            btnSnooze.BackColor = Color.FromArgb(22, 71, 117);
            btnSnooze.Dock = DockStyle.Fill;
            btnSnooze.FlatStyle = FlatStyle.Flat;
            btnSnooze.Font = new Font("Novus-Regular", 11F, FontStyle.Regular, GraphicsUnit.Point, 0);
            btnSnooze.ForeColor = Color.FromArgb(234, 242, 248);
            btnSnooze.ImagesProperties.BackgroundImageSize = new Size(18, 18);
            btnSnooze.ImagesProperties.ImageFixedSize = new Size(24, 24);
            btnSnooze.ImagesProperties.ImageMaxSize = new Size(24, 24);
            btnSnooze.ImagesProperties.ImageSizeMode = Desktop.Controls.Core.Helper.Enums.SizeMode.Stretch;
            btnSnooze.ImagesProperties.OriginalImageSize = new Size(18, 18);
            btnSnooze.Location = new Point(148, 45);
            btnSnooze.Margin = new Padding(4);
            btnSnooze.MinimumSize = new Size(64, 36);
            btnSnooze.Name = "btnSnooze";
            btnSnooze.PaletteProperties.CustomBackColor = Desktop.Controls.Core.Helper.Enums.ColorsList.Color1;
            btnSnooze.PaletteProperties.CustomFont = Desktop.Controls.Core.Helper.Enums.FontsList.Font1;
            btnSnooze.PaletteProperties.CustomForeColor = Desktop.Controls.Core.Helper.Enums.ColorsList.Color8;
            btnSnooze.RadiusProperties.BorderColor = Color.Transparent;
            btnSnooze.RadiusProperties.BorderRadius = 10;
            btnSnooze.RadiusProperties.BorderSize = 0;
            btnSnooze.RightToLeft = RightToLeft.No;
            btnSnooze.Size = new Size(136, 62);
            btnSnooze.TabIndex = 0;
            btnSnooze.Text = "Snooze";
            btnSnooze.UseVisualStyleBackColor = true;
            // 
            // SnoozePopupView
            // 
            AutoScaleDimensions = new SizeF(9F, 21F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(434, 111);
            Controls.Add(tlpMain);
            Margin = new Padding(4);
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "SnoozePopupView";
            StartPosition = FormStartPosition.CenterScreen;
            Text = "SnoozePopupView";
            tlpMain.ResumeLayout(false);
            ResumeLayout(false);
        }

        #endregion

        private LeadTeamsTableLayoutPanel tlpMain;
        private LeadTeamsLabeledDateTimePicker dtpSnooze;
        private LeadTeamsButton btnShutdown;
        private LeadTeamsButton btnStartWork;
        private LeadTeamsButton btnSnooze;
    }
}