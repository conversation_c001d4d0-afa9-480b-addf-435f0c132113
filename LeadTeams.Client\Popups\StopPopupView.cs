﻿namespace LeadTeams.Client.Popups
{
    public partial class StopPopupView : LeadTeamsForm
    {
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public Enums.StopPopUpResult StopPopUpResult { get; private set; }

        private readonly ISession _session;
        private readonly Stopwatch _stopwatch;
        private readonly int _maxWaitTime;
        private readonly SemaphoreSlim _semaphore;
        private readonly CancellationTokenSource _cts;
        private readonly Task _stopTask;

        //For design time support
        public StopPopupView() : base()
        {
            InitializeComponent();
        }

        public StopPopupView(ISession session)
        {
            _session = session;
            _session.AppLogger.LogInformation($"Initialize Component");
            InitializeComponent();
            _session.AppLogger.LogInformation($"Associate And Raise Events");
            AssociateAndRaiseViewEvents();
            _stopwatch = new Stopwatch();
            _semaphore = new SemaphoreSlim(1, 1);
            _cts = new CancellationTokenSource();
            _maxWaitTime = _session.Setting?.StopPopUpMaxWaitTime ?? 10;
            _stopwatch.Start();
            _stopTask = Task.Run(() => CheckingStopStatus(_cts.Token));
        }

        private void AssociateAndRaiseViewEvents()
        {
            btnRemindme.Click += (s, e) => SetResult(Enums.StopPopUpResult.Remindme);
            btnNo.Click += (s, e) => SetResult(Enums.StopPopUpResult.No);
        }

        private async Task CheckingStopStatus(CancellationToken cancellationToken)
        {
            _session.AppLogger.LogDebug($"Start Checking Stop Status");

            while (!cancellationToken.IsCancellationRequested)
            {
                await _semaphore.WaitAsync(cancellationToken);
                try
                {
                    TimeSpan elapsed = _stopwatch.Elapsed;
                    lblHeader.Text = $"Waiting Time\n{elapsed:hh\\:mm\\:ss} / {TimeSpan.FromSeconds(_maxWaitTime)}";

                    if (elapsed.TotalSeconds >= _maxWaitTime)
                    {
                        SetResult(Enums.StopPopUpResult.No);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show(ex.Message);
                }
                finally
                {
                    _semaphore.Release();
                }

                await Task.Delay(1000, cancellationToken);
            }
        }

        private void SetResult(Enums.StopPopUpResult result)
        {
            _session.AppLogger.LogDebug($"Setting StopPopUpResult to [{result}]");
            StopPopUpResult = result;
            DialogResult = DialogResult.OK;
        }

        protected override void OnClosed(EventArgs e)
        {
            _session.AppLogger.LogInformation($"Disposing instance");
            _cts.Cancel();
            _semaphore.Dispose();
            _stopwatch.Stop();
            base.OnClosed(e);
        }
    }
}
