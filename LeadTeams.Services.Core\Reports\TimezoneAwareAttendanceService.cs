//namespace LeadTeams.Services.Core.Reports
//{
//    /// <summary>
//    /// Attendance service with timezone-aware SQL queries.
//    /// </summary>
//    public class TimezoneAwareAttendanceService : ITimezoneAwareAttendanceService
//    {
//        private readonly ApplicationDbContext _context;
//        private readonly AttendanceTimezoneRepository _attendanceRepository;

//        public TimezoneAwareAttendanceService(ApplicationDbContext context)
//        {
//            _context = context;
//            _attendanceRepository = new AttendanceTimezoneRepository(context);
//        }

//        /// <summary>
//        /// Gets attendance logs with automatic timezone conversion in SQL.
//        /// </summary>
//        public async Task<List<AttendanceLogWithTimezone>> GetAttendanceLogsWithTimezoneAsync(
//            DateTime startDateUtc,
//            DateTime endDateUtc,
//            string timezone,
//            Ulid? employeeId = null,
//            Ulid? organizationId = null)
//        {
//            return await _attendanceRepository.GetAttendanceWithTimezoneAsync(
//                startDateUtc,
//                endDateUtc,
//                timezone,
//                employeeId);
//        }

//        /// <summary>
//        /// Gets attendance summary grouped by period with timezone conversion.
//        /// </summary>
//        public async Task<List<AttendanceSummaryWithTimezone>> GetAttendanceSummaryWithTimezoneAsync(
//            DateTime startDateUtc,
//            DateTime endDateUtc,
//            string timezone,
//            string groupBy = "day",
//            Ulid? employeeId = null,
//            Ulid? organizationId = null)
//        {
//            return await _attendanceRepository.GetAttendanceSummaryWithTimezoneAsync(
//                startDateUtc,
//                endDateUtc,
//                timezone,
//                groupBy);
//        }

//        /// <summary>
//        /// Gets real-time attendance data with timezone conversion.
//        /// </summary>
//        public async Task<List<RealTimeAttendanceWithTimezone>> GetRealTimeAttendanceWithTimezoneAsync(
//            string timezone,
//            Ulid? organizationId = null)
//        {
//            var sql = @"
//                SELECT 
//                    a.Id,
//                    a.EmployeeId,
//                    e.Name AS EmployeeName,
//                    a.StartDateTime,
//                    a.StartDateTime AT TIME ZONE 'UTC' AT TIME ZONE {0} AS StartDateTimeLocal,
//                    CASE 
//                        WHEN a.EndDateTime IS NULL THEN NULL
//                        ELSE a.EndDateTime AT TIME ZONE 'UTC' AT TIME ZONE {0}
//                    END AS EndDateTimeLocal,
//                    CASE 
//                        WHEN a.EndDateTime IS NULL THEN 
//                            DATEDIFF(SECOND, a.StartDateTime, GETUTCDATE())
//                        ELSE a.TotalSeconds
//                    END AS CurrentTotalSeconds,
//                    CASE 
//                        WHEN a.EndDateTime IS NULL THEN 'Active'
//                        ELSE 'Completed'
//                    END AS Status
//                FROM AttendanceLogModels a
//                INNER JOIN EmployeeModels e ON a.EmployeeId = e.Id
//                WHERE a.StartDateTime >= CAST(GETUTCDATE() AS DATE)";

//            if (organizationId.HasValue)
//                sql += " AND a.OrganizationId = {1}";

//            sql += " ORDER BY a.StartDateTime DESC";

//            var parameters = organizationId.HasValue
//                ? new object[] { timezone, organizationId.Value.ToByteArray() }
//                : new object[] { timezone };

//            return await _context.Database
//                .SqlQueryRaw<RealTimeAttendanceWithTimezone>(sql, parameters)
//                .ToListAsync();
//        }

//        /// <summary>
//        /// Gets attendance statistics with timezone-aware calculations.
//        /// </summary>
//        public async Task<AttendanceStatisticsWithTimezone> GetAttendanceStatisticsWithTimezoneAsync(
//            DateTime startDateUtc,
//            DateTime endDateUtc,
//            string timezone,
//            Ulid? employeeId = null,
//            Ulid? organizationId = null)
//        {
//            var sql = @"
//                WITH TimezoneAttendance AS (
//                    SELECT 
//                        EmployeeId,
//                        StartDateTime AT TIME ZONE 'UTC' AT TIME ZONE {0} AS StartLocal,
//                        EndDateTime AT TIME ZONE 'UTC' AT TIME ZONE {0} AS EndLocal,
//                        TotalSeconds,
//                        CAST(StartDateTime AT TIME ZONE 'UTC' AT TIME ZONE {0} AS DATE) AS AttendanceDate
//                    FROM AttendanceLogModels
//                    WHERE StartDateTime >= {1} AND StartDateTime <= {2}";

//            if (employeeId.HasValue)
//                sql += " AND EmployeeId = {3}";
//            if (organizationId.HasValue)
//                sql += " AND OrganizationId = {4}";

//            sql += @"
//                )
//                SELECT 
//                    COUNT(DISTINCT EmployeeId) AS TotalEmployees,
//                    COUNT(*) AS TotalSessions,
//                    SUM(TotalSeconds) AS TotalWorkingSeconds,
//                    AVG(TotalSeconds) AS AverageSessionSeconds,
//                    COUNT(DISTINCT AttendanceDate) AS TotalWorkingDays,
//                    MIN(StartLocal) AS EarliestStart,
//                    MAX(EndLocal) AS LatestEnd,
//                    AVG(CAST(DATEPART(HOUR, StartLocal) AS FLOAT) + CAST(DATEPART(MINUTE, StartLocal) AS FLOAT) / 60) AS AverageStartHour,
//                    AVG(CAST(DATEPART(HOUR, EndLocal) AS FLOAT) + CAST(DATEPART(MINUTE, EndLocal) AS FLOAT) / 60) AS AverageEndHour
//                FROM TimezoneAttendance";

//            var parameters = new List<object> { timezone, startDateUtc, endDateUtc };
//            if (employeeId.HasValue) parameters.Add(employeeId.Value.ToByteArray());
//            if (organizationId.HasValue) parameters.Add(organizationId.Value.ToByteArray());

//            var result = await _context.Database
//                .SqlQueryRaw<AttendanceStatisticsWithTimezone>(sql, parameters.ToArray())
//                .FirstOrDefaultAsync();

//            return result ?? new AttendanceStatisticsWithTimezone();
//        }

//        /// <summary>
//        /// Gets attendance heatmap data with timezone conversion.
//        /// </summary>
//        public async Task<List<AttendanceHeatmapData>> GetAttendanceHeatmapWithTimezoneAsync(
//            DateTime startDateUtc,
//            DateTime endDateUtc,
//            string timezone,
//            Ulid? organizationId = null)
//        {
//            var sql = @"
//                SELECT 
//                    DATEPART(HOUR, StartDateTime AT TIME ZONE 'UTC' AT TIME ZONE {0}) AS Hour,
//                    DATEPART(WEEKDAY, StartDateTime AT TIME ZONE 'UTC' AT TIME ZONE {0}) AS DayOfWeek,
//                    COUNT(*) AS SessionCount,
//                    SUM(TotalSeconds) AS TotalSeconds
//                FROM AttendanceLogModels
//                WHERE StartDateTime >= {1} AND StartDateTime <= {2}";

//            if (organizationId.HasValue)
//                sql += " AND OrganizationId = {3}";

//            sql += @"
//                GROUP BY 
//                    DATEPART(HOUR, StartDateTime AT TIME ZONE 'UTC' AT TIME ZONE {0}),
//                    DATEPART(WEEKDAY, StartDateTime AT TIME ZONE 'UTC' AT TIME ZONE {0})
//                ORDER BY DayOfWeek, Hour";

//            var parameters = organizationId.HasValue
//                ? new object[] { timezone, startDateUtc, endDateUtc, organizationId.Value.ToByteArray() }
//                : new object[] { timezone, startDateUtc, endDateUtc };

//            return await _context.Database
//                .SqlQueryRaw<AttendanceHeatmapData>(sql, parameters)
//                .ToListAsync();
//        }
//    }

//    /// <summary>
//    /// Interface for timezone-aware attendance service.
//    /// </summary>
//    public interface ITimezoneAwareAttendanceService
//    {
//        Task<List<AttendanceLogWithTimezone>> GetAttendanceLogsWithTimezoneAsync(
//            DateTime startDateUtc, DateTime endDateUtc, string timezone,
//            Ulid? employeeId = null, Ulid? organizationId = null);

//        Task<List<AttendanceSummaryWithTimezone>> GetAttendanceSummaryWithTimezoneAsync(
//            DateTime startDateUtc, DateTime endDateUtc, string timezone, string groupBy = "day",
//            Ulid? employeeId = null, Ulid? organizationId = null);

//        Task<List<RealTimeAttendanceWithTimezone>> GetRealTimeAttendanceWithTimezoneAsync(
//            string timezone, Ulid? organizationId = null);

//        Task<AttendanceStatisticsWithTimezone> GetAttendanceStatisticsWithTimezoneAsync(
//            DateTime startDateUtc, DateTime endDateUtc, string timezone,
//            Ulid? employeeId = null, Ulid? organizationId = null);

//        Task<List<AttendanceHeatmapData>> GetAttendanceHeatmapWithTimezoneAsync(
//            DateTime startDateUtc, DateTime endDateUtc, string timezone, Ulid? organizationId = null);
//    }

//    /// <summary>
//    /// Model for real-time attendance with timezone information.
//    /// </summary>
//    public class RealTimeAttendanceWithTimezone
//    {
//        public Ulid Id { get; set; }
//        public byte[] EmployeeId { get; set; } = Array.Empty<byte>();
//        public string EmployeeName { get; set; } = string.Empty;
//        public DateTime StartDateTime { get; set; }
//        public DateTime StartDateTimeLocal { get; set; }
//        public DateTime? EndDateTimeLocal { get; set; }
//        public long CurrentTotalSeconds { get; set; }
//        public string Status { get; set; } = string.Empty;
//    }

//    /// <summary>
//    /// Model for attendance statistics with timezone information.
//    /// </summary>
//    public class AttendanceStatisticsWithTimezone
//    {
//        public int TotalEmployees { get; set; }
//        public int TotalSessions { get; set; }
//        public long TotalWorkingSeconds { get; set; }
//        public double AverageSessionSeconds { get; set; }
//        public int TotalWorkingDays { get; set; }
//        public DateTime? EarliestStart { get; set; }
//        public DateTime? LatestEnd { get; set; }
//        public double AverageStartHour { get; set; }
//        public double AverageEndHour { get; set; }
//    }

//    /// <summary>
//    /// Model for attendance heatmap data.
//    /// </summary>
//    public class AttendanceHeatmapData
//    {
//        public int Hour { get; set; }
//        public int DayOfWeek { get; set; }
//        public int SessionCount { get; set; }
//        public long TotalSeconds { get; set; }
//    }
//}
