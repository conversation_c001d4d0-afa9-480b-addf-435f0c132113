﻿namespace LeadTeams.Client.Popups
{
    partial class InMeetingPopupView
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            tlpMain = new LeadTeamsTableLayoutPanel();
            lblMeetingDuration = new LeadTeamsLabel();
            lblMeetingName = new LeadTeamsLabel();
            tlpMain.SuspendLayout();
            SuspendLayout();
            // 
            // tlpMain
            // 
            tlpMain.BackColor = Color.FromArgb(234, 242, 248);
            tlpMain.ColumnCount = 2;
            tlpMain.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.3333321F));
            tlpMain.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.3333321F));
            tlpMain.Controls.Add(lblMeetingDuration, 0, 1);
            tlpMain.Controls.Add(lblMeetingName, 0, 0);
            tlpMain.Dock = DockStyle.Fill;
            tlpMain.ForeColor = Color.FromArgb(22, 71, 117);
            tlpMain.Location = new Point(0, 0);
            tlpMain.Margin = new Padding(4);
            tlpMain.Name = "tlpMain";
            tlpMain.RightToLeft = RightToLeft.No;
            tlpMain.RowCount = 2;
            tlpMain.RowStyles.Add(new RowStyle(SizeType.Percent, 50F));
            tlpMain.RowStyles.Add(new RowStyle(SizeType.Percent, 50F));
            tlpMain.Size = new Size(134, 81);
            tlpMain.TabIndex = 1;
            // 
            // lblMeetingDuration
            // 
            lblMeetingDuration.Anchor = AnchorStyles.Left | AnchorStyles.Right;
            lblMeetingDuration.AutoSize = true;
            lblMeetingDuration.BackColor = Color.FromArgb(234, 242, 248);
            tlpMain.SetColumnSpan(lblMeetingDuration, 2);
            lblMeetingDuration.FlatStyle = FlatStyle.Flat;
            lblMeetingDuration.Font = new Font("Novus-Regular", 11F, FontStyle.Regular, GraphicsUnit.Point, 0);
            lblMeetingDuration.ForeColor = Color.FromArgb(22, 71, 117);
            lblMeetingDuration.Location = new Point(4, 42);
            lblMeetingDuration.Margin = new Padding(4, 0, 4, 0);
            lblMeetingDuration.MinimumSize = new Size(64, 36);
            lblMeetingDuration.Name = "lblMeetingDuration";
            lblMeetingDuration.PaletteProperties.CustomBackColor = Desktop.Controls.Core.Helper.Enums.ColorsList.Default;
            lblMeetingDuration.PaletteProperties.CustomFont = Desktop.Controls.Core.Helper.Enums.FontsList.Font1;
            lblMeetingDuration.PaletteProperties.CustomForeColor = Desktop.Controls.Core.Helper.Enums.ColorsList.Color1;
            lblMeetingDuration.RadiusProperties.BorderColor = Color.Transparent;
            lblMeetingDuration.RadiusProperties.BorderRadius = 10;
            lblMeetingDuration.RadiusProperties.BorderSize = 0;
            lblMeetingDuration.RightToLeft = RightToLeft.No;
            lblMeetingDuration.Size = new Size(126, 36);
            lblMeetingDuration.TabIndex = 0;
            lblMeetingDuration.Text = "...";
            lblMeetingDuration.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // lblMeetingName
            // 
            lblMeetingName.AutoSize = true;
            lblMeetingName.BackColor = Color.FromArgb(234, 242, 248);
            tlpMain.SetColumnSpan(lblMeetingName, 2);
            lblMeetingName.Dock = DockStyle.Fill;
            lblMeetingName.FlatStyle = FlatStyle.Flat;
            lblMeetingName.Font = new Font("Novus Light", 9F, FontStyle.Bold, GraphicsUnit.Point, 0);
            lblMeetingName.ForeColor = Color.FromArgb(22, 71, 117);
            lblMeetingName.Location = new Point(3, 0);
            lblMeetingName.MinimumSize = new Size(50, 26);
            lblMeetingName.Name = "lblMeetingName";
            lblMeetingName.PaletteProperties.CustomBackColor = Desktop.Controls.Core.Helper.Enums.ColorsList.Default;
            lblMeetingName.PaletteProperties.CustomFont = Desktop.Controls.Core.Helper.Enums.FontsList.Font3;
            lblMeetingName.PaletteProperties.CustomForeColor = Desktop.Controls.Core.Helper.Enums.ColorsList.Color1;
            lblMeetingName.RadiusProperties.BorderColor = Color.Transparent;
            lblMeetingName.RadiusProperties.BorderRadius = 10;
            lblMeetingName.RadiusProperties.BorderSize = 0;
            lblMeetingName.RightToLeft = RightToLeft.No;
            lblMeetingName.Size = new Size(128, 40);
            lblMeetingName.TabIndex = 1;
            lblMeetingName.Text = "...";
            // 
            // InMeetingPopupView
            // 
            AutoScaleDimensions = new SizeF(9F, 21F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(134, 81);
            Controls.Add(tlpMain);
            Margin = new Padding(4);
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "InMeetingPopupView";
            StartPosition = FormStartPosition.CenterScreen;
            Text = "InMeetingPopupView";
            TopMost = true;
            tlpMain.ResumeLayout(false);
            tlpMain.PerformLayout();
            ResumeLayout(false);
        }

        #endregion

        private LeadTeamsTableLayoutPanel tlpMain;
        private LeadTeamsLabel lblMeetingDuration;
        private LeadTeamsLabel lblMeetingName;
    }
}