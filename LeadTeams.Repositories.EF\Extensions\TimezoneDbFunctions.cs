//using Microsoft.EntityFrameworkCore;

//namespace LeadTeams.Repositories.EF.Extensions
//{
//    /// <summary>
//    /// Custom database functions for timezone conversion in EF Core queries.
//    /// </summary>
//    public static class TimezoneDbFunctions
//    {
//        /// <summary>
//        /// Converts UTC DateTime to specified timezone in SQL Server.
//        /// </summary>
//        /// <param name="functions">EF.Functions</param>
//        /// <param name="utcDateTime">UTC DateTime to convert</param>
//        /// <param name="targetTimezone">Target timezone (e.g., 'Eastern Standard Time')</param>
//        /// <returns>DateTime in target timezone</returns>
//        public static DateTime ConvertUtcToTimezone(this DbFunctions functions, DateTime utcDateTime, string targetTimezone)
//        {
//            throw new NotSupportedException("This method is for use in EF Core queries only.");
//        }

//        /// <summary>
//        /// Converts UTC DateTime to specified timezone offset in SQL Server.
//        /// </summary>
//        /// <param name="functions">EF.Functions</param>
//        /// <param name="utcDateTime">UTC DateTime to convert</param>
//        /// <param name="timezoneOffset">Timezone offset (e.g., '-05:00')</param>
//        /// <returns>DateTime in target timezone</returns>
//        public static DateTime ConvertUtcToOffset(this DbFunctions functions, DateTime utcDateTime, string timezoneOffset)
//        {
//            throw new NotSupportedException("This method is for use in EF Core queries only.");
//        }

//        /// <summary>
//        /// Converts UTC DateTimeOffset to specified timezone in SQL Server.
//        /// </summary>
//        /// <param name="functions">EF.Functions</param>
//        /// <param name="utcDateTime">UTC DateTimeOffset to convert</param>
//        /// <param name="targetTimezone">Target timezone (e.g., 'Eastern Standard Time')</param>
//        /// <returns>DateTimeOffset in target timezone</returns>
//        public static DateTimeOffset ConvertUtcToTimezoneOffset(this DbFunctions functions, DateTimeOffset utcDateTime, string targetTimezone)
//        {
//            throw new NotSupportedException("This method is for use in EF Core queries only.");
//        }

//        /// <summary>
//        /// Gets the current date/time in specified timezone.
//        /// </summary>
//        /// <param name="functions">EF.Functions</param>
//        /// <param name="timezone">Target timezone</param>
//        /// <returns>Current DateTime in specified timezone</returns>
//        public static DateTime GetCurrentTimeInTimezone(this DbFunctions functions, string timezone)
//        {
//            throw new NotSupportedException("This method is for use in EF Core queries only.");
//        }

//        /// <summary>
//        /// Formats DateTime with timezone information for display.
//        /// </summary>
//        /// <param name="functions">EF.Functions</param>
//        /// <param name="dateTime">DateTime to format</param>
//        /// <param name="format">Format string</param>
//        /// <param name="timezone">Timezone for conversion</param>
//        /// <returns>Formatted string</returns>
//        public static string FormatDateTimeWithTimezone(this DbFunctions functions, DateTime dateTime, string format, string timezone)
//        {
//            throw new NotSupportedException("This method is for use in EF Core queries only.");
//        }
//    }

//    /// <summary>
//    /// Extension methods for configuring timezone database functions in DbContext.
//    /// </summary>
//    public static class TimezoneDbContextExtensions
//    {
//        /// <summary>
//        /// Configures timezone database functions for SQL Server.
//        /// </summary>
//        public static void ConfigureSqlServerTimezoneFunctions(this ModelBuilder modelBuilder)
//        {
//            // SQL Server timezone conversion functions
//            modelBuilder.HasDbFunction(typeof(TimezoneDbFunctions).GetMethod(nameof(TimezoneDbFunctions.ConvertUtcToTimezone))!)
//                .HasTranslation(args => new Microsoft.EntityFrameworkCore.Query.SqlExpressions.SqlFunctionExpression(
//                    "CONVERT",
//                    new[]
//                    {
//                        new Microsoft.EntityFrameworkCore.Query.SqlExpressions.SqlFragmentExpression("DATETIME"),
//                        new Microsoft.EntityFrameworkCore.Query.SqlExpressions.SqlBinaryExpression(
//                            Microsoft.EntityFrameworkCore.Query.SqlExpressions.ExpressionType.AtTimeZone,
//                            new Microsoft.EntityFrameworkCore.Query.SqlExpressions.SqlBinaryExpression(
//                                Microsoft.EntityFrameworkCore.Query.SqlExpressions.ExpressionType.AtTimeZone,
//                                args.First(),
//                                new Microsoft.EntityFrameworkCore.Query.SqlExpressions.SqlConstantExpression("UTC", typeof(string))
//                            ),
//                            args.Skip(1).First(),
//                            typeof(DateTimeOffset)
//                        )
//                    },
//                    nullable: true,
//                    argumentsPropagateNullability: new[] { true, true },
//                    typeof(DateTime)
//                ));

//            modelBuilder.HasDbFunction(typeof(TimezoneDbFunctions).GetMethod(nameof(TimezoneDbFunctions.ConvertUtcToOffset))!)
//                .HasTranslation(args => new Microsoft.EntityFrameworkCore.Query.SqlExpressions.SqlFunctionExpression(
//                    "DATEADD",
//                    new[]
//                    {
//                        new Microsoft.EntityFrameworkCore.Query.SqlExpressions.SqlConstantExpression("HOUR", typeof(string)),
//                        new Microsoft.EntityFrameworkCore.Query.SqlExpressions.SqlFunctionExpression(
//                            "CAST",
//                            new[]
//                            {
//                                new Microsoft.EntityFrameworkCore.Query.SqlExpressions.SqlFunctionExpression(
//                                    "LEFT",
//                                    new[]
//                                    {
//                                        args.Skip(1).First(),
//                                        new Microsoft.EntityFrameworkCore.Query.SqlExpressions.SqlConstantExpression(3, typeof(int))
//                                    },
//                                    nullable: true,
//                                    argumentsPropagateNullability: new[] { true, false },
//                                    typeof(string)
//                                ),
//                                new Microsoft.EntityFrameworkCore.Query.SqlExpressions.SqlFragmentExpression("INT")
//                            },
//                            nullable: true,
//                            argumentsPropagateNullability: new[] { true, false },
//                            typeof(int)
//                        ),
//                        args.First()
//                    },
//                    nullable: true,
//                    argumentsPropagateNullability: new[] { false, true, true },
//                    typeof(DateTime)
//                ));

//            modelBuilder.HasDbFunction(typeof(TimezoneDbFunctions).GetMethod(nameof(TimezoneDbFunctions.GetCurrentTimeInTimezone))!)
//                .HasTranslation(args => new Microsoft.EntityFrameworkCore.Query.SqlExpressions.SqlBinaryExpression(
//                    Microsoft.EntityFrameworkCore.Query.SqlExpressions.ExpressionType.AtTimeZone,
//                    new Microsoft.EntityFrameworkCore.Query.SqlExpressions.SqlBinaryExpression(
//                        Microsoft.EntityFrameworkCore.Query.SqlExpressions.ExpressionType.AtTimeZone,
//                        new Microsoft.EntityFrameworkCore.Query.SqlExpressions.SqlFunctionExpression(
//                            "GETUTCDATE",
//                            Array.Empty<Microsoft.EntityFrameworkCore.Query.SqlExpressions.SqlExpression>(),
//                            nullable: false,
//                            argumentsPropagateNullability: Array.Empty<bool>(),
//                            typeof(DateTime)
//                        ),
//                        new Microsoft.EntityFrameworkCore.Query.SqlExpressions.SqlConstantExpression("UTC", typeof(string))
//                    ),
//                    args.First(),
//                    typeof(DateTimeOffset)
//                ));
//        }

//        /// <summary>
//        /// Configures timezone database functions for PostgreSQL.
//        /// </summary>
//        public static void ConfigurePostgreSqlTimezoneFunctions(this ModelBuilder modelBuilder)
//        {
//            modelBuilder.HasDbFunction(typeof(TimezoneDbFunctions).GetMethod(nameof(TimezoneDbFunctions.ConvertUtcToTimezone))!)
//                .HasTranslation(args => new Microsoft.EntityFrameworkCore.Query.SqlExpressions.SqlFunctionExpression(
//                    "timezone",
//                    new[] { args.Skip(1).First(), args.First() },
//                    nullable: true,
//                    argumentsPropagateNullability: new[] { true, true },
//                    typeof(DateTime)
//                ));

//            modelBuilder.HasDbFunction(typeof(TimezoneDbFunctions).GetMethod(nameof(TimezoneDbFunctions.GetCurrentTimeInTimezone))!)
//                .HasTranslation(args => new Microsoft.EntityFrameworkCore.Query.SqlExpressions.SqlFunctionExpression(
//                    "timezone",
//                    new[]
//                    {
//                        args.First(),
//                        new Microsoft.EntityFrameworkCore.Query.SqlExpressions.SqlFunctionExpression(
//                            "now",
//                            Array.Empty<Microsoft.EntityFrameworkCore.Query.SqlExpressions.SqlExpression>(),
//                            nullable: false,
//                            argumentsPropagateNullability: Array.Empty<bool>(),
//                            typeof(DateTime)
//                        )
//                    },
//                    nullable: true,
//                    argumentsPropagateNullability: new[] { true, false },
//                    typeof(DateTime)
//                ));
//        }

//        /// <summary>
//        /// Configures timezone database functions for MySQL.
//        /// </summary>
//        public static void ConfigureMySqlTimezoneFunctions(this ModelBuilder modelBuilder)
//        {
//            modelBuilder.HasDbFunction(typeof(TimezoneDbFunctions).GetMethod(nameof(TimezoneDbFunctions.ConvertUtcToOffset))!)
//                .HasTranslation(args => new Microsoft.EntityFrameworkCore.Query.SqlExpressions.SqlFunctionExpression(
//                    "CONVERT_TZ",
//                    new[]
//                    {
//                        args.First(),
//                        new Microsoft.EntityFrameworkCore.Query.SqlExpressions.SqlConstantExpression("+00:00", typeof(string)),
//                        args.Skip(1).First()
//                    },
//                    nullable: true,
//                    argumentsPropagateNullability: new[] { true, false, true },
//                    typeof(DateTime)
//                ));

//            modelBuilder.HasDbFunction(typeof(TimezoneDbFunctions).GetMethod(nameof(TimezoneDbFunctions.GetCurrentTimeInTimezone))!)
//                .HasTranslation(args => new Microsoft.EntityFrameworkCore.Query.SqlExpressions.SqlFunctionExpression(
//                    "CONVERT_TZ",
//                    new[]
//                    {
//                        new Microsoft.EntityFrameworkCore.Query.SqlExpressions.SqlFunctionExpression(
//                            "UTC_TIMESTAMP",
//                            Array.Empty<Microsoft.EntityFrameworkCore.Query.SqlExpressions.SqlExpression>(),
//                            nullable: false,
//                            argumentsPropagateNullability: Array.Empty<bool>(),
//                            typeof(DateTime)
//                        ),
//                        new Microsoft.EntityFrameworkCore.Query.SqlExpressions.SqlConstantExpression("+00:00", typeof(string)),
//                        args.First()
//                    },
//                    nullable: true,
//                    argumentsPropagateNullability: new[] { false, false, true },
//                    typeof(DateTime)
//                ));
//        }
//    }
//}
